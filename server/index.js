const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const ffmpeg = require('fluent-ffmpeg');
const { spawn } = require('child_process');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: true, // Allow all origins
    methods: ["GET", "POST"],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

app.use(cors({
  origin: true, // Allow all origins for now
  credentials: true
}));
app.use(express.json());

// Serve static files from the dist directory
app.use(express.static(path.join(__dirname, '../dist')));

// Store room information
const productions = new Map();

class Production {
  constructor(id) {
    this.id = id;
    this.host = null;
    this.participants = new Map();
    this.rtmpStream = null;
  }

  addUser(socketId, isHost = false) {
    if (isHost) {
      this.host = socketId;
    } else {
      this.participants.set(socketId, {
        id: socketId,
        joinedAt: Date.now()
      });
    }
  }

  removeUser(socketId) {
    if (this.host === socketId) {
      this.host = null;
      // Stop RTMP stream if host leaves
      this.stopRTMP();
    } else {
      this.participants.delete(socketId);
    }
  }

  getUsers() {
    const users = [];
    if (this.host) users.push(this.host);
    users.push(...this.participants.keys());
    return users;
  }

  isEmpty() {
    return !this.host && this.participants.size === 0;
  }

  async startRTMP(rtmpUrl, streamKey) {
    if (this.rtmpStream) {
      await this.stopRTMP();
    }

    console.log(`Starting RTMP stream for room ${this.id}`);

    try {
      // Note: RTMP streaming now handled by separate mediasoup service
      // This is just for tracking state in the signaling server
      this.rtmpStream = {
        url: rtmpUrl,
        key: streamKey,
        startedAt: Date.now(),
        process: null // No local process needed
      };

      return true;
    } catch (error) {
      console.error(`Failed to start RTMP stream for room ${this.id}:`, error);
      return false;
    }
  }

  async stopRTMP() {
    if (this.rtmpStream) {
      console.log(`Stopping RTMP stream for room ${this.id}`);
      this.rtmpStream = null;
      return true;
    }
    return false;
  }

  cleanup() {
    // No mediasoup cleanup needed - handled by separate service
  }
}

io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  socket.on('join-production', ({ productionId, isHost }) => {
    console.log(`\n=== JOIN PRODUCTION REQUEST ===`);
    console.log(`User ${socket.id} joining production ${productionId} as ${isHost ? 'host' : 'participant'}`);

    // Create production if it doesn't exist
    if (!productions.has(productionId)) {
      console.log(`Creating new production: ${productionId}`);
      productions.set(productionId, new Production(productionId));
    } else {
      console.log(`Production ${productionId} already exists`);
    }

    const production = productions.get(productionId);
    console.log(`Production ${productionId} current state:`, {
      host: production.host,
      participants: Array.from(production.participants.keys()),
      totalUsers: production.getUsers().length
    });

    // Check if production already has a host and user is trying to be host
    if (isHost && production.host) {
      console.log(`ERROR: Production ${productionId} already has host ${production.host}, rejecting ${socket.id}`);
      socket.emit('error', { message: 'Production already has a host' });
      return;
    }

    // Join the production
    console.log(`Adding ${socket.id} to Socket.IO production ${productionId}`);
    socket.join(productionId);

    console.log(`Adding ${socket.id} to production data structure as ${isHost ? 'host' : 'participant'}`);
    production.addUser(socket.id, isHost);
    socket.productionId = productionId;
    socket.isHost = isHost;

    // Get existing users before notifying
    const existingUsers = production.getUsers().filter(id => id !== socket.id);
    console.log(`Existing users in production ${productionId}:`, existingUsers);

    // Notify existing users about new user
    console.log(`Notifying ${existingUsers.length} existing users about new user ${socket.id}`);
    socket.to(productionId).emit('user-joined', { peerId: socket.id });

    // Send existing users to new user
    console.log(`Sending list of ${existingUsers.length} existing users to new user ${socket.id}`);
    existingUsers.forEach(userId => {
      console.log(`  - Sending user-joined event for ${userId} to ${socket.id}`);
      socket.emit('user-joined', { peerId: userId });
    });

    // If the new user is a participant, tell existing users (hosts) to initiate connection
    if (!isHost && existingUsers.length > 0) {
      console.log(`New user ${socket.id} is participant, telling ${existingUsers.length} existing users to initiate connection`);
      existingUsers.forEach(userId => {
        console.log(`  - Sending initiate-connection to ${userId} for participant ${socket.id}`);
        socket.to(userId).emit('initiate-connection', { peerId: socket.id });
      });
    } else if (isHost && existingUsers.length > 0) {
      console.log(`New user ${socket.id} is host, existing participants should initiate connection to host`);
      existingUsers.forEach(userId => {
        console.log(`  - Sending initiate-connection to participant ${userId} for host ${socket.id}`);
        socket.to(userId).emit('initiate-connection', { peerId: socket.id });
      });
    } else {
      console.log(`No existing users to notify about connection initiation`);
    }

    console.log(`Production ${productionId} final state:`, {
      host: production.host,
      participants: Array.from(production.participants.keys()),
      totalUsers: production.getUsers().length
    });
    console.log(`=== JOIN PRODUCTION COMPLETE ===\n`);
  });

  socket.on('offer', ({ offer, to }) => {
    console.log(`\n=== OFFER RELAY ===`);
    console.log(`Relaying offer from ${socket.id} to ${to}`);
    console.log(`Offer type: ${offer?.type}, SDP length: ${offer?.sdp?.length || 0}`);
    socket.to(to).emit('offer', {
      offer,
      from: socket.id
    });
    console.log(`Offer relayed successfully`);
    console.log(`=== OFFER RELAY COMPLETE ===\n`);
  });

  socket.on('answer', ({ answer, to }) => {
    console.log(`\n=== ANSWER RELAY ===`);
    console.log(`Relaying answer from ${socket.id} to ${to}`);
    console.log(`Answer type: ${answer?.type}, SDP length: ${answer?.sdp?.length || 0}`);
    socket.to(to).emit('answer', {
      answer,
      from: socket.id
    });
    console.log(`Answer relayed successfully`);
    console.log(`=== ANSWER RELAY COMPLETE ===\n`);
  });

  socket.on('ice-candidate', ({ candidate, to }) => {
    console.log(`\n=== ICE CANDIDATE RELAY ===`);
    console.log(`Relaying ICE candidate from ${socket.id} to ${to}`);
    console.log(`Candidate type: ${candidate?.type}, protocol: ${candidate?.protocol}`);
    console.log(`Candidate string: ${candidate?.candidate}`);
    
    // Check if target socket exists
    const targetSocket = io.sockets.sockets.get(to);
    if (!targetSocket) {
      console.error(`❌ Target socket ${to} not found for ICE candidate relay`);
      return;
    }
    
    socket.to(to).emit('ice-candidate', {
      candidate,
      from: socket.id
    });
    console.log(`✅ ICE candidate relayed successfully to ${to}`);
    console.log(`=== ICE CANDIDATE RELAY COMPLETE ===\n`);
  });

  socket.on('start-rtmp', async ({ rtmpUrl, streamKey }) => {
    if (!socket.isHost || !socket.productionId) {
      socket.emit('error', { message: 'Only hosts can start RTMP streams' });
      return;
    }

    const production = productions.get(socket.productionId);
    if (production) {
      try {
        const success = await production.startRTMP(rtmpUrl, streamKey);
        socket.emit('rtmp-status', {
          streaming: success,
          message: success ? 'RTMP stream started' : 'Failed to start RTMP stream'
        });
      } catch (error) {
        socket.emit('rtmp-status', {
          streaming: false,
          message: 'Failed to start RTMP stream: ' + error.message
        });
      }
    }
  });

  socket.on('stop-rtmp', async () => {
    if (!socket.isHost || !socket.productionId) {
      socket.emit('error', { message: 'Only hosts can stop RTMP streams' });
      return;
    }

    const production = productions.get(socket.productionId);
    if (production) {
      try {
        const success = await production.stopRTMP();
        socket.emit('rtmp-status', {
          streaming: false,
          message: success ? 'RTMP stream stopped' : 'No active stream to stop'
        });
      } catch (error) {
        socket.emit('rtmp-status', {
          streaming: false,
          message: 'Failed to stop RTMP stream: ' + error.message
        });
      }
    }
  });



  socket.on('disconnect', () => {
    console.log(`\n=== USER DISCONNECT ===`);
    console.log(`User disconnected: ${socket.id}`);

    if (socket.productionId) {
      console.log(`User ${socket.id} was in production ${socket.productionId}`);
      const production = productions.get(socket.productionId);
      if (production) {
        console.log(`Production ${socket.productionId} before removal:`, {
          host: production.host,
          participants: Array.from(production.participants.keys()),
          totalUsers: production.getUsers().length
        });

        production.removeUser(socket.id);
        console.log(`Removed ${socket.id} from production ${socket.productionId}`);

        // Notify other users
        console.log(`Notifying other users in production ${socket.productionId} about departure`);
        socket.to(socket.productionId).emit('user-left', { peerId: socket.id });

        // Clean up empty productions
        if (production.isEmpty()) {
          console.log(`Production ${socket.productionId} is now empty, removing it`);
          production.cleanup();
          productions.delete(socket.productionId);
        } else {
          console.log(`Production ${socket.productionId} after removal:`, {
            host: production.host,
            participants: Array.from(production.participants.keys()),
            totalUsers: production.getUsers().length
          });
        }
      } else {
        console.log(`Production ${socket.productionId} not found when trying to remove user`);
      }
    } else {
      console.log(`User ${socket.id} was not in any production`);
    }
    console.log(`=== USER DISCONNECT COMPLETE ===\n`);
  });
});

// Add health check endpoint
app.get('/', (req, res) => {
  res.json({
    status: 'WebRTC Signaling Server Running',
    timestamp: new Date().toISOString()
  });
});

// Configuration endpoint for frontend
app.get('/api/config', (req, res) => {
  res.json({
    mediaServerUrl: process.env.MEDIA_SERVER_URL || 'http://localhost:8081',
    turnServer: process.env.VITE_TURN_SERVER,
    turnUsername: process.env.VITE_TURN_USERNAME,
    turnPassword: process.env.VITE_TURN_PASSWORD
  });
});

// Get production info
app.get('/productions/:productionId/info', (req, res) => {
  const { productionId } = req.params;
  const production = productions.get(productionId);

  if (!production) {
    return res.status(404).json({ error: 'Production not found' });
  }

  res.json({
    id: production.id,
    hasHost: !!production.host,
    participantCount: production.participants.size,
    isStreaming: !!production.rtmpStream
  });
});

// Catch-all handler: send back React's index.html file for client-side routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist/index.html'));
});

const PORT = process.env.PORT || 3001;

// Start server
server.listen(PORT, () => {
  console.log(`🚀 WebRTC Signaling Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');

  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
