{"name": "sai-signaling-server", "version": "1.0.0", "description": "NestJS signaling server for multi-peer streaming with authentication", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "dev": "nest start --watch"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/mongoose": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/websockets": "^10.0.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cors": "^2.8.5", "fluent-ffmpeg": "^2.1.2", "googleapis": "^157.0.0", "mongoose": "^8.0.0", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.4"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.0.0", "@types/passport": "^1.0.16", "@types/passport-facebook": "^3.0.3", "@types/passport-google-oauth20": "^2.0.14", "@types/passport-local": "^1.0.38", "nodemon": "^3.0.2", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "typescript": "^5.1.3"}}