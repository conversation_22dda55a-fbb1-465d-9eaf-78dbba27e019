import { IsE<PERSON>, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>E<PERSON>, MinLength, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class LinkedPlatformDto {
  @IsEnum(['google', 'facebook', 'local'])
  provider: string;

  @IsString()
  providerId: string;

  @IsOptional()
  @IsString()
  accessToken?: string;

  @IsOptional()
  @IsString()
  refreshToken?: string;

  @IsOptional()
  tokenExpiresAt?: Date;
}

export class CreateUserDto {
  @IsEmail()
  email: string;

  @IsString()
  @MinLength(6)
  @IsOptional()
  password?: string;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  avatar?: string;

  @IsEnum(['local', 'google', 'facebook'])
  @IsOptional()
  provider?: string = 'local';

  @IsOptional()
  @IsString()
  providerId?: string;

  @IsOptional()
  @IsString()
  accessToken?: string;

  @IsOptional()
  @IsString()
  refreshToken?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LinkedPlatformDto)
  linkedPlatforms?: LinkedPlatformDto[];
}
