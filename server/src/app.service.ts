import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getHealth() {
    return {
      status: 'NestJS Signaling Server Running',
      timestamp: new Date().toISOString(),
    };
  }

  getConfig() {
    return {
      mediaServerUrl: process.env.MEDIA_SERVER_URL || 'http://localhost:8081',
      turnServer: process.env.VITE_TURN_SERVER,
      turnUsername: process.env.VITE_TURN_USERNAME,
      turnPassword: process.env.VITE_TURN_PASSWORD,
    };
  }
}
