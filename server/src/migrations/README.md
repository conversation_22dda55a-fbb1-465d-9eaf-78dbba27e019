# Database Migration: Events and Productions Merge

This directory contains migration scripts for merging the Events and Productions entities into a single unified Production entity.

## Overview

The migration consolidates the previously separate Event and Production entities into a single Production entity that handles both scheduling and live production functionality.

### Changes Made

1. **Schema Changes:**
   - Events schema removed
   - Productions schema updated to include scheduling fields
   - Status values updated: `active` → `live`, added `scheduled` status

2. **API Changes:**
   - Events endpoints moved to Productions controller
   - Event-specific functionality merged into Productions service
   - Client-side code updated to use Productions endpoints

## Migration Process

### Prerequisites

1. Backup your database before running the migration
2. Ensure the new server code is deployed
3. Stop all running instances of the application

### Running the Migration

```bash
# Navigate to the server directory
cd server

# Set your MongoDB connection string
export MONGODB_URI="your-mongodb-connection-string"

# Run the migration script
node src/migrations/merge-events-productions.js
```

### What the Migration Does

1. **Migrates Events to Productions:**
   - Converts each Event document to a Production document
   - Maps event fields to corresponding production fields
   - Preserves all scheduling and YouTube integration data

2. **Updates Existing Productions:**
   - Adds new fields (description, scheduledStartTime, etc.) with default values
   - Updates status from `active` to `live` where applicable

3. **Preserves Data:**
   - No data is lost during migration
   - Original events collection is preserved (not dropped) for safety

### Post-Migration Steps

1. **Verify Migration:**
   - Check that all events have been converted to productions
   - Verify that existing productions still work correctly
   - Test the application functionality

2. **Clean Up (Optional):**
   ```javascript
   // After verifying migration success, you can drop the events collection
   db.events.drop()
   ```

## Rollback Plan

If you need to rollback:

1. Restore from your database backup
2. Deploy the previous version of the application
3. The events collection will still exist if you haven't dropped it

## Testing

After migration, test the following:

1. **Scheduled Productions:** Create, edit, delete scheduled productions
2. **Go Live:** Convert scheduled productions to live
3. **Production Management:** Join, leave, complete productions
4. **YouTube Integration:** Verify YouTube broadcasts still work
5. **Historical Data:** Check that completed productions display correctly

## Schema Mapping

| Event Field | Production Field | Notes |
|-------------|------------------|-------|
| `name` | `name` | Direct mapping |
| `description` | `description` | Direct mapping |
| `scheduledStartTime` | `scheduledStartTime` | Direct mapping |
| `hostUserId` | `hostUserId` | Direct mapping |
| `status` | `status` | `scheduled`/`live`/`completed`/`cancelled` |
| `shareableProductionId` | `productionId` | Direct mapping |
| `wentLiveAt` | `wentLiveAt` | Direct mapping |
| `completedAt` | `completedAt` | Direct mapping |
| `youtubeBroadcastId` | `youtubeBroadcastId` | Direct mapping |
| `youtubeStreamId` | `youtubeStreamId` | Direct mapping |
| `youtubePrivacy` | `youtubePrivacy` | Direct mapping |

## Support

If you encounter issues during migration:

1. Check the migration logs for specific error messages
2. Verify your MongoDB connection string
3. Ensure you have sufficient database permissions
4. Contact the development team for assistance
