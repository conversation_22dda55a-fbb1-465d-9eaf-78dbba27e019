/**
 * Migration script to merge Events and Productions into a single Production entity
 * 
 * This script:
 * 1. Migrates existing Event documents to Production documents
 * 2. Updates existing Production documents to include new fields
 * 3. Removes the events collection
 * 
 * Run this script after deploying the new unified Production schema
 */

const { MongoClient } = require('mongodb');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/sai-platform';

async function migrateEventsToProductions() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    const eventsCollection = db.collection('events');
    const productionsCollection = db.collection('productions');
    
    // Get all events
    const events = await eventsCollection.find({}).toArray();
    console.log(`Found ${events.length} events to migrate`);
    
    for (const event of events) {
      console.log(`Migrating event: ${event.name} (${event._id})`);
      
      // Check if there's already a production for this event
      let existingProduction = null;
      if (event.productionId) {
        existingProduction = await productionsCollection.findOne({ _id: event.productionId });
      }
      
      if (existingProduction) {
        // Update existing production with event data
        await productionsCollection.updateOne(
          { _id: event.productionId },
          {
            $set: {
              description: event.description,
              scheduledStartTime: event.scheduledStartTime,
              wentLiveAt: event.wentLiveAt,
              completedAt: event.completedAt,
              youtubeBroadcastId: event.youtubeBroadcastId,
              youtubeStreamId: event.youtubeStreamId,
              youtubePrivacy: event.youtubePrivacy || 'unlisted',
              // Update status mapping
              status: event.status === 'scheduled' ? 'scheduled' : 
                     event.status === 'live' ? 'live' : 
                     event.status === 'completed' ? 'completed' : 'cancelled'
            }
          }
        );
        console.log(`Updated existing production for event ${event.name}`);
      } else {
        // Create new production from event
        const newProduction = {
          productionId: event.shareableProductionId,
          name: event.name,
          description: event.description,
          hostUserId: event.hostUserId,
          participantUserIds: [],
          scheduledStartTime: event.scheduledStartTime,
          wentLiveAt: event.wentLiveAt,
          completedAt: event.completedAt,
          isStreaming: false,
          status: event.status === 'scheduled' ? 'scheduled' : 
                 event.status === 'live' ? 'live' : 
                 event.status === 'completed' ? 'completed' : 'cancelled',
          lastActivityAt: event.updatedAt || event.createdAt || new Date(),
          youtubeBroadcastId: event.youtubeBroadcastId,
          youtubeStreamId: event.youtubeStreamId,
          youtubePrivacy: event.youtubePrivacy || 'unlisted',
          createdAt: event.createdAt || new Date(),
          updatedAt: event.updatedAt || new Date()
        };
        
        await productionsCollection.insertOne(newProduction);
        console.log(`Created new production from event ${event.name}`);
      }
    }
    
    // Update existing productions to include new fields if they don't exist
    const productions = await productionsCollection.find({}).toArray();
    console.log(`Updating ${productions.length} existing productions with new fields`);
    
    for (const production of productions) {
      const updates = {};
      
      // Add missing fields with defaults
      if (!production.hasOwnProperty('description')) {
        updates.description = undefined;
      }
      if (!production.hasOwnProperty('scheduledStartTime')) {
        updates.scheduledStartTime = undefined;
      }
      if (!production.hasOwnProperty('wentLiveAt')) {
        updates.wentLiveAt = undefined;
      }
      if (!production.hasOwnProperty('completedAt')) {
        updates.completedAt = undefined;
      }
      if (!production.hasOwnProperty('youtubePrivacy')) {
        updates.youtubePrivacy = 'unlisted';
      }
      
      // Update status if it's the old 'active' status
      if (production.status === 'active') {
        updates.status = 'live';
      }
      
      if (Object.keys(updates).length > 0) {
        await productionsCollection.updateOne(
          { _id: production._id },
          { $set: updates }
        );
        console.log(`Updated production ${production.name} with new fields`);
      }
    }
    
    console.log('Migration completed successfully');
    console.log('Note: The events collection has NOT been dropped automatically for safety.');
    console.log('After verifying the migration, you can manually drop it with: db.events.drop()');
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    await client.close();
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  migrateEventsToProductions()
    .then(() => {
      console.log('Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateEventsToProductions };
