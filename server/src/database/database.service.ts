import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class DatabaseService implements OnModuleInit {
  private connectionString: string;

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    // Try to get connection string from environment variable first
    this.connectionString = this.configService.get<string>('MONGODB_URI');

    if (!this.connectionString) {
      // If not in env, try to read from the Terraform-generated config file
      try {
        const configPath = path.join(__dirname, '../../mongodb-config.json');
        if (fs.existsSync(configPath)) {
          const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

          // Check if connection_string already includes credentials
          if (config.connection_string.includes('@')) {
            // Connection string already has credentials
            this.connectionString = `${config.connection_string}/${config.database}`;
          } else {
            // Need to add credentials to connection string
            const baseUrl = config.connection_string.replace('mongodb+srv://', '');
            this.connectionString = `mongodb+srv://${encodeURIComponent(config.username)}:${encodeURIComponent(config.password)}@${baseUrl}/${config.database}`;
          }
          console.log('📦 Using MongoDB Atlas from Terraform config');
        }
      } catch (error) {
        console.warn('⚠️ Could not read MongoDB config file:', error.message);
      }
    }

    if (!this.connectionString) {
      throw new Error('MongoDB connection string not found. Please set MONGODB_URI environment variable or ensure mongodb-config.json exists.');
    }

    console.log('🔗 MongoDB Atlas connection configured');
    console.log(`📊 Database: ${this.getDatabaseName()}`);
  }

  async getConnectionString(): Promise<string> {
    if (!this.connectionString) {
      await this.onModuleInit();
    }
    return this.connectionString;
  }

  getDatabaseName(): string {
    if (this.connectionString) {
      // Extract database name from connection string
      const match = this.connectionString.match(/\/([^/?]+)(\?|$)/);
      return match ? match[1] : 'switcher-ai';
    }
    return 'switcher-ai';
  }

  getDbPath(): string {
    return 'MongoDB Atlas';
  }
}
