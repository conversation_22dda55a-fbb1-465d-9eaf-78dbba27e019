import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { NestExpressApplication } from '@nestjs/platform-express';
import { AppModule } from './app.module';
import * as path from 'path';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // Enable CORS
  app.enableCors({
    origin: true, // Allow all origins for now
    credentials: true,
  });

  // Enable validation pipes
  app.useGlobalPipes(new ValidationPipe());

  // Serve static files from the dist directory
  app.useStaticAssets(path.join(__dirname, '../../dist'));

  const port = process.env.PORT || 3001;
  await app.listen(port);

  console.log(`🚀 NestJS Signaling Server running on port ${port}`);
  console.log(`📊 Health check: http://localhost:${port}/health`);
}

bootstrap();
