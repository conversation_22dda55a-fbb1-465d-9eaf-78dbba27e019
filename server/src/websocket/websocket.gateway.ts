import {
  WebSocketGateway as WSGateway,
  SubscribeMessage,
  MessageBody,
  WebSocketServer,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { ProductionsService } from '../productions/productions.service';
import { UsersService } from '../users/users.service';
import { Logger } from '@nestjs/common';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  productionId?: string;
  isHost?: boolean;
}

@WSGateway({
  cors: {
    origin: true,
    credentials: true,
  },
  transports: ['websocket', 'polling'],
})
export class SignalingGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(SignalingGateway.name);

  constructor(
    private jwtService: JwtService,
    private productionsService: ProductionsService,
    private usersService: UsersService,
  ) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      // Extract token from handshake
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
      
      if (!token) {
        this.logger.warn(`Client ${client.id} connected without token`);
        client.emit('error', { message: 'Authentication required' });
        client.disconnect();
        return;
      }

      // Verify JWT token
      const payload = this.jwtService.verify(token);
      client.userId = payload.sub;
      
      this.logger.log(`Authenticated user ${client.userId} connected: ${client.id}`);
    } catch (error) {
      this.logger.warn(`Client ${client.id} authentication failed: ${error.message}`);
      client.emit('error', { message: 'Invalid token' });
      client.disconnect();
    }
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    this.logger.log(`User ${client.userId} disconnected: ${client.id}`);

    if (client.productionId && client.userId) {
      try {
        // Remove user from production
        await this.productionsService.removeParticipant(client.productionId, client.userId);

        // Update user's current production
        await this.usersService.updateCurrentProduction(client.userId, null);

        // Notify other users in the production
        client.to(client.productionId).emit('user-left', { peerId: client.id, userId: client.userId });

        this.logger.log(`Removed user ${client.userId} from production ${client.productionId}`);
      } catch (error) {
        this.logger.error(`Error removing user from production: ${error.message}`);
      }
    }
  }

  @SubscribeMessage('join-production')
  async handleJoinProduction(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { productionId: string; isHost: boolean },
  ) {
    try {
      this.logger.log(`User ${client.userId} joining production ${data.productionId} as ${data.isHost ? 'host' : 'participant'}`);

      if (!client.userId) {
        client.emit('error', { message: 'Not authenticated' });
        return;
      }

      // Find or validate production
      let production = await this.productionsService.findByProductionId(data.productionId);

      if (data.isHost) {
        if (production) {
          client.emit('error', { message: 'Production already exists' });
          return;
        }
        // This shouldn't happen as productions should be created via REST API
        client.emit('error', { message: 'Production must be created via API first' });
        return;
      } else {
        if (!production) {
          client.emit('error', { message: 'Production not found' });
          return;
        }

        // Add participant to production
        await this.productionsService.addParticipant(data.productionId, client.userId, client.userId);
      }

      // Update user's current production
      await this.usersService.updateCurrentProduction(client.userId, production._id as any);

      // Join socket room
      client.join(data.productionId);
      client.productionId = data.productionId;
      client.isHost = data.isHost;

      // Get existing users in the production
      const socketsInProduction = await this.server.in(data.productionId).fetchSockets();
      const existingUsers = socketsInProduction
        .filter(s => s.id !== client.id)
        .map(s => ({ peerId: s.id, userId: (s as any).userId }));

      // Notify existing users about new user
      client.to(data.productionId).emit('user-joined', {
        peerId: client.id,
        userId: client.userId
      });

      // Send existing users to new user
      existingUsers.forEach(user => {
        client.emit('user-joined', user);
      });

      // Handle connection initiation logic
      if (!data.isHost && existingUsers.length > 0) {
        existingUsers.forEach(user => {
          client.to(user.peerId).emit('initiate-connection', { 
            peerId: client.id, 
            userId: client.userId 
          });
        });
      }

      this.logger.log(`User ${client.userId} successfully joined production ${data.productionId}`);
    } catch (error) {
      this.logger.error(`Error joining room: ${error.message}`);
      client.emit('error', { message: 'Failed to join room' });
    }
  }

  @SubscribeMessage('offer')
  handleOffer(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { offer: any; to: string },
  ) {
    this.logger.log(`Relaying offer from ${client.id} to ${data.to}`);
    client.to(data.to).emit('offer', {
      offer: data.offer,
      from: client.id,
      fromUserId: client.userId,
    });
  }

  @SubscribeMessage('answer')
  handleAnswer(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { answer: any; to: string },
  ) {
    this.logger.log(`Relaying answer from ${client.id} to ${data.to}`);
    client.to(data.to).emit('answer', {
      answer: data.answer,
      from: client.id,
      fromUserId: client.userId,
    });
  }

  @SubscribeMessage('ice-candidate')
  handleIceCandidate(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { candidate: any; to: string },
  ) {
    this.logger.log(`Relaying ICE candidate from ${client.id} to ${data.to}`);
    client.to(data.to).emit('ice-candidate', {
      candidate: data.candidate,
      from: client.id,
      fromUserId: client.userId,
    });
  }

  @SubscribeMessage('start-rtmp')
  async handleStartRtmp(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { rtmpUrl: string; streamKey: string },
  ) {
    try {
      if (!client.isHost || !client.productionId || !client.userId) {
        client.emit('error', { message: 'Only hosts can start RTMP streams' });
        return;
      }

      await this.productionsService.startStreaming(
        client.productionId,
        data.rtmpUrl,
        data.streamKey,
        client.userId,
      );

      client.emit('rtmp-status', {
        streaming: true,
        message: 'RTMP stream started',
      });
    } catch (error) {
      client.emit('rtmp-status', {
        streaming: false,
        message: 'Failed to start RTMP stream: ' + error.message,
      });
    }
  }

  @SubscribeMessage('stop-rtmp')
  async handleStopRtmp(@ConnectedSocket() client: AuthenticatedSocket) {
    try {
      if (!client.isHost || !client.productionId || !client.userId) {
        client.emit('error', { message: 'Only hosts can stop RTMP streams' });
        return;
      }

      await this.productionsService.stopStreaming(client.productionId, client.userId);

      client.emit('rtmp-status', {
        streaming: false,
        message: 'RTMP stream stopped',
      });
    } catch (error) {
      client.emit('rtmp-status', {
        streaming: false,
        message: 'Failed to stop RTMP stream: ' + error.message,
      });
    }
  }
}
