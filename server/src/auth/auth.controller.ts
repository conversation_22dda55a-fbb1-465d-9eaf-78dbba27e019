import { Controller, Request, Post, UseGuards, Body, Get, Res, Param } from '@nestjs/common';
import { Response } from 'express';
import { AuthService } from './auth.service';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { GoogleAuthGuard } from './guards/google-auth.guard';
import { FacebookAuthGuard } from './guards/facebook-auth.guard';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('register')
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @UseGuards(LocalAuthGuard)
  @Post('login')
  async login(@Request() req, @Body() loginDto: LoginDto) {
    return this.authService.login(req.user);
  }

  @Get('google')
  @UseGuards(GoogleAuthGuard)
  async googleAuth(@Request() req) {
    // Guard redirects to Google
  }

  @Get('google/callback')
  @UseGuards(GoogleAuthGuard)
  async googleAuthRedirect(@Request() req, @Res() res: Response) {
    // Check if this is a linking operation by looking at the state parameter
    const isLinking = req.query?.state === 'linking';

    if (isLinking) {
      // This is a linking operation - send HTML that closes the popup
      const html = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>Linking Complete</title>
          </head>
          <body>
            <script>
              // Send message to parent window and close popup
              if (window.opener) {
                window.opener.postMessage({ type: 'OAUTH_SUCCESS', provider: 'google' }, '*');
                window.close();
              } else {
                // Fallback if no opener
                window.location.href = '${process.env.FRONTEND_URL || 'http://localhost:5173'}';
              }
            </script>
            <p>Linking successful! This window should close automatically.</p>
          </body>
        </html>
      `;
      res.send(html);
    } else {
      // Regular login flow
      const result = await this.authService.login(req.user);
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      res.redirect(`${frontendUrl}/auth/callback?token=${result.access_token}`);
    }
  }

  @Get('facebook')
  @UseGuards(FacebookAuthGuard)
  async facebookAuth(@Request() req) {
    // Guard redirects to Facebook
  }

  @Get('facebook/callback')
  @UseGuards(FacebookAuthGuard)
  async facebookAuthRedirect(@Request() req, @Res() res: Response) {
    // Check if this is a linking operation by looking at the state parameter
    const isLinking = req.query?.state === 'linking';

    if (isLinking) {
      // This is a linking operation - send HTML that closes the popup
      const html = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>Linking Complete</title>
          </head>
          <body>
            <script>
              // Send message to parent window and close popup
              if (window.opener) {
                window.opener.postMessage({ type: 'OAUTH_SUCCESS', provider: 'facebook' }, '*');
                window.close();
              } else {
                // Fallback if no opener
                window.location.href = '${process.env.FRONTEND_URL || 'http://localhost:5173'}';
              }
            </script>
            <p>Linking successful! This window should close automatically.</p>
          </body>
        </html>
      `;
      res.send(html);
    } else {
      // Regular login flow
      const result = await this.authService.login(req.user);
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      res.redirect(`${frontendUrl}/auth/callback?token=${result.access_token}`);
    }
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  async getProfile(@Request() req) {
    return this.authService.getProfile(req.user.userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('platforms')
  async getPlatformStatus(@Request() req) {
    return this.authService.getPlatformStatus(req.user.userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('platforms/:provider/unlink')
  async unlinkPlatform(@Request() req, @Param('provider') provider: string) {
    return this.authService.unlinkPlatform(req.user.userId, provider);
  }

  @Get('google/link')
  async googleLinkAuth(@Res() res: Response) {
    // Redirect to Google OAuth with a state parameter to indicate this is a linking operation
    const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
      `client_id=${process.env.GOOGLE_CLIENT_ID}&` +
      `redirect_uri=${encodeURIComponent(process.env.GOOGLE_CALLBACK_URL || 'http://localhost:3001/auth/google/callback')}&` +
      `response_type=code&` +
      `scope=${encodeURIComponent('email profile https://www.googleapis.com/auth/youtube https://www.googleapis.com/auth/youtube.force-ssl')}&` +
      `state=linking`;

    res.redirect(authUrl);
  }



  @Get('facebook/link')
  async facebookLinkAuth(@Res() res: Response) {
    // Redirect to Facebook OAuth with a state parameter to indicate this is a linking operation
    const authUrl = `https://www.facebook.com/v18.0/dialog/oauth?` +
      `client_id=${process.env.FACEBOOK_APP_ID}&` +
      `redirect_uri=${encodeURIComponent(process.env.FACEBOOK_CALLBACK_URL || 'http://localhost:3001/auth/facebook/callback')}&` +
      `scope=email&` +
      `state=linking`;

    res.redirect(authUrl);
  }



  @UseGuards(JwtAuthGuard)
  @Post('change-password')
  async changePassword(@Request() req, @Body() changePasswordDto: ChangePasswordDto) {
    return this.authService.changePassword(req.user.userId, changePasswordDto);
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  async logout(@Request() req) {
    // For JWT, logout is handled client-side by removing the token
    return { message: 'Logged out successfully' };
  }
}
