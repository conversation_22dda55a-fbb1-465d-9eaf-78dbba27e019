import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { UserDocument } from '../users/schemas/user.schema';
import { CreateUserDto } from '../users/dto/create-user.dto';
import { RegisterDto } from './dto/register.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.usersService.findByEmail(email);
    if (user && await this.usersService.validatePassword(user, password)) {
      const { password, ...result } = user.toObject();
      return result;
    }
    return null;
  }

  async validateOAuthUser(profile: {
    email: string;
    name: string;
    avatar?: string;
    provider: string;
    providerId: string;
    accessToken?: string;
    refreshToken?: string;
  }): Promise<UserDocument> {
    // First try to find user by provider ID
    let user = await this.usersService.findByProvider(profile.provider, profile.providerId);

    if (!user) {
      // Try to find by email (user might have registered with email/password first)
      user = await this.usersService.findByEmail(profile.email);

      if (user) {
        // Update existing user with OAuth info and tokens
        user.provider = profile.provider;
        user.providerId = profile.providerId;
        if (profile.avatar && !user.avatar) {
          user.avatar = profile.avatar;
        }
        if (profile.accessToken) {
          user.accessToken = profile.accessToken;
          user.tokenExpiresAt = new Date(Date.now() + 3600 * 1000); // 1 hour from now
        }
        if (profile.refreshToken) {
          user.refreshToken = profile.refreshToken;
        }

        // Also add to linkedPlatforms if not already there (only for OAuth providers)
        if (profile.provider !== 'local') {
          const existingPlatform = user.linkedPlatforms.find(p => p.provider === profile.provider);
          if (!existingPlatform) {
            user.linkedPlatforms.push({
              provider: profile.provider,
              providerId: profile.providerId,
              accessToken: profile.accessToken,
              refreshToken: profile.refreshToken,
              tokenExpiresAt: profile.accessToken ? new Date(Date.now() + 3600 * 1000) : undefined,
              linkedAt: new Date(),
            });
          } else {
            // Update existing platform
            existingPlatform.accessToken = profile.accessToken;
            existingPlatform.refreshToken = profile.refreshToken;
            existingPlatform.tokenExpiresAt = profile.accessToken ? new Date(Date.now() + 3600 * 1000) : undefined;
          }
        }

        await user.save();
      } else {
        // Create new user
        const linkedPlatforms = profile.provider !== 'local' ? [{
          provider: profile.provider,
          providerId: profile.providerId,
          accessToken: profile.accessToken,
          refreshToken: profile.refreshToken,
          tokenExpiresAt: profile.accessToken ? new Date(Date.now() + 3600 * 1000) : undefined,
          linkedAt: new Date(),
        }] : [];

        const createUserDto: CreateUserDto = {
          email: profile.email,
          name: profile.name,
          avatar: profile.avatar,
          provider: profile.provider,
          providerId: profile.providerId,
          accessToken: profile.accessToken,
          refreshToken: profile.refreshToken,
          linkedPlatforms,
        };
        user = await this.usersService.create(createUserDto);
      }
    } else {
      // Update existing OAuth user tokens
      if (profile.accessToken) {
        user.accessToken = profile.accessToken;
        user.tokenExpiresAt = new Date(Date.now() + 3600 * 1000);
      }
      if (profile.refreshToken) {
        user.refreshToken = profile.refreshToken;
      }

      // Update linkedPlatforms (only for OAuth providers)
      if (profile.provider !== 'local') {
        const existingPlatform = user.linkedPlatforms.find(p => p.provider === profile.provider);
        if (existingPlatform) {
          existingPlatform.accessToken = profile.accessToken;
          existingPlatform.refreshToken = profile.refreshToken;
          existingPlatform.tokenExpiresAt = profile.accessToken ? new Date(Date.now() + 3600 * 1000) : undefined;
        } else {
          user.linkedPlatforms.push({
            provider: profile.provider,
            providerId: profile.providerId,
            accessToken: profile.accessToken,
            refreshToken: profile.refreshToken,
            tokenExpiresAt: profile.accessToken ? new Date(Date.now() + 3600 * 1000) : undefined,
            linkedAt: new Date(),
          });
        }
      }

      await user.save();
    }

    return user;
  }

  async login(user: any) {
    const payload = { 
      email: user.email, 
      sub: user._id,
      name: user.name 
    };
    
    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        avatar: user.avatar,
        currentRoomId: user.currentRoomId,
      },
    };
  }

  async register(registerDto: RegisterDto) {
    const createUserDto: CreateUserDto = {
      email: registerDto.email,
      password: registerDto.password,
      name: registerDto.name,
      provider: 'local',
    };

    const user = await this.usersService.create(createUserDto);
    return this.login(user);
  }

  async getProfile(userId: string) {
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    return {
      id: user._id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      currentProductionId: user.currentProductionId,
      provider: user.provider,
      lastLoginAt: user.lastLoginAt,
      hasPassword: !!user.password,
      linkedPlatforms: user.linkedPlatforms.map(platform => ({
        provider: platform.provider,
        providerId: platform.providerId,
        linkedAt: platform.linkedAt,
        hasValidToken: platform.accessToken && (!platform.tokenExpiresAt || platform.tokenExpiresAt > new Date()),
      })),
    };
  }

  async linkPlatform(userId: string, platformData: {
    provider: string;
    providerId: string;
    accessToken?: string;
    refreshToken?: string;
  }) {
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Check if platform is already linked
    const existingPlatform = user.linkedPlatforms.find(p => p.provider === platformData.provider);
    if (existingPlatform) {
      // Update existing platform
      existingPlatform.providerId = platformData.providerId;
      existingPlatform.accessToken = platformData.accessToken;
      existingPlatform.refreshToken = platformData.refreshToken;
      existingPlatform.tokenExpiresAt = platformData.accessToken ? new Date(Date.now() + 3600 * 1000) : undefined;
    } else {
      // Add new platform
      user.linkedPlatforms.push({
        provider: platformData.provider,
        providerId: platformData.providerId,
        accessToken: platformData.accessToken,
        refreshToken: platformData.refreshToken,
        tokenExpiresAt: platformData.accessToken ? new Date(Date.now() + 3600 * 1000) : undefined,
        linkedAt: new Date(),
      });
    }

    await user.save();
    return this.getProfile(userId);
  }

  async unlinkPlatform(userId: string, provider: string) {
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Don't allow unlinking if it would leave user with no authentication methods
    const hasPassword = !!user.password;
    const otherLinkedPlatforms = user.linkedPlatforms.filter(p => p.provider !== provider);

    if (user.provider === provider && !hasPassword && otherLinkedPlatforms.length === 0) {
      throw new BadRequestException('Cannot unlink the only authentication method. Please set a password first.');
    }

    // Remove platform from linkedPlatforms
    user.linkedPlatforms = user.linkedPlatforms.filter(p => p.provider !== provider);

    // If this was the primary provider, update to local or another linked platform
    if (user.provider === provider) {
      if (hasPassword) {
        user.provider = 'local';
        user.providerId = undefined;
        user.accessToken = undefined;
        user.refreshToken = undefined;
        user.tokenExpiresAt = undefined;
      } else if (user.linkedPlatforms.length > 0) {
        const newPrimary = user.linkedPlatforms[0];
        user.provider = newPrimary.provider;
        user.providerId = newPrimary.providerId;
        user.accessToken = newPrimary.accessToken;
        user.refreshToken = newPrimary.refreshToken;
        user.tokenExpiresAt = newPrimary.tokenExpiresAt;
      }
    }

    await user.save();
    return this.getProfile(userId);
  }

  async getPlatformStatus(userId: string) {
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const platforms = {
      google: {
        connected: false,
        linkedAt: null,
        hasValidToken: false,
      },
      facebook: {
        connected: false,
        linkedAt: null,
        hasValidToken: false,
      },
    };

    // Check primary provider
    if (user.provider === 'google' || user.provider === 'facebook') {
      platforms[user.provider].connected = true;
      platforms[user.provider].hasValidToken = user.accessToken && (!user.tokenExpiresAt || user.tokenExpiresAt > new Date());
    }

    // Check linked platforms
    user.linkedPlatforms.forEach(platform => {
      if (platform.provider === 'google' || platform.provider === 'facebook') {
        platforms[platform.provider].connected = true;
        platforms[platform.provider].linkedAt = platform.linkedAt;
        platforms[platform.provider].hasValidToken = platform.accessToken && (!platform.tokenExpiresAt || platform.tokenExpiresAt > new Date());
      }
    });

    return {
      hasPassword: !!user.password,
      platforms,
    };
  }

  async changePassword(userId: string, changePasswordDto: ChangePasswordDto) {
    const user = await this.usersService.findById(userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const { currentPassword, newPassword } = changePasswordDto;

    // If user has a password, verify current password
    if (user.password) {
      if (!currentPassword) {
        throw new BadRequestException('Current password is required');
      }

      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        throw new BadRequestException('Current password is incorrect');
      }
    }

    // Hash new password
    const saltRounds = 10;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update user password
    user.password = hashedNewPassword;

    // Note: We don't need to add 'local' to linkedPlatforms since having a password
    // is tracked by the password field itself. linkedPlatforms is for OAuth providers only.

    await user.save();

    return {
      message: 'Password changed successfully',
      hasPassword: true,
    };
  }
}
