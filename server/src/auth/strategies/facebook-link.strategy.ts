import { PassportStrategy } from '@nestjs/passport';
import { Strategy, Profile } from 'passport-facebook';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth.service';

@Injectable()
export class FacebookLinkStrategy extends PassportStrategy(Strategy, 'facebook-link') {
  constructor(
    private configService: ConfigService,
    private authService: AuthService,
  ) {
    const clientID = configService.get<string>('FACEBOOK_APP_ID');
    const clientSecret = configService.get<string>('FACEBOOK_APP_SECRET');

    if (!clientID || !clientSecret) {
      // Skip initialization if credentials are not provided
      return;
    }

    super({
      clientID,
      clientSecret,
      callbackURL: 'http://localhost:3001/auth/facebook/link/callback',
      scope: 'email',
      profileFields: ['emails', 'name', 'picture'],
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: Profile,
    done: (err: any, user: any, info?: any) => void,
  ): Promise<any> {
    const { id, name, emails, photos } = profile;

    const user = await this.authService.validateOAuthUser({
      email: emails[0].value,
      name: name.givenName + ' ' + name.familyName,
      avatar: photos[0]?.value,
      provider: 'facebook',
      providerId: id,
      accessToken,
      refreshToken,
    });

    // Mark this as a linking operation
    (user as any).isLinking = true;

    done(null, user);
  }
}
