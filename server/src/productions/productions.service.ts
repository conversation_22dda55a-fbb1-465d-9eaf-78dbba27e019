import { Injectable, ConflictException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Production, ProductionDocument } from './schemas/production.schema';
import { CreateProductionDto } from './dto/create-production.dto';
import { UpdateProductionDto } from './dto/update-production.dto';
import { YouTubeService } from '../youtube/youtube.service';

@Injectable()
export class ProductionsService {
  constructor(
    @InjectModel(Production.name) private productionModel: Model<ProductionDocument>,
    private youtubeService: YouTubeService,
  ) {}

  private generateProductionId(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }

  async create(createProductionDto: CreateProductionDto, hostUserId: string): Promise<ProductionDocument> {
    // Check if user already has a live production (scheduled productions are allowed)
    const existingLiveProduction = await this.productionModel.findOne({
      hostUserId: new Types.ObjectId(hostUserId),
      status: 'live'
    }).exec();

    if (existingLiveProduction) {
      throw new ConflictException('User already has a live production. Complete the current production before creating a new one.');
    }

    let productionId = createProductionDto.productionId;

    // Generate production ID if not provided
    if (!productionId) {
      productionId = this.generateProductionId();

      // Ensure uniqueness
      let attempts = 0;
      while (await this.productionModel.findOne({ productionId }).exec() && attempts < 10) {
        productionId = this.generateProductionId();
        attempts++;
      }

      if (attempts >= 10) {
        throw new ConflictException('Unable to generate unique production ID');
      }
    } else {
      // Check if custom production ID already exists
      const existingProduction = await this.productionModel.findOne({ productionId, status: { $in: ['scheduled', 'live'] } }).exec();
      if (existingProduction) {
        throw new ConflictException('Production with this ID already exists');
      }
    }

    // Determine initial status based on whether scheduledStartTime is provided
    let initialStatus: 'scheduled' | 'live' = 'live'; // Default to live for immediate productions
    let scheduledStartTime: Date | undefined;

    if (createProductionDto.scheduledStartTime) {
      scheduledStartTime = new Date(createProductionDto.scheduledStartTime);
      const now = new Date();

      // Allow productions scheduled for now (within 1 minute) for "Go Live Now" functionality
      // Otherwise, validate that the scheduled time is in the future
      const timeDifferenceMinutes = (scheduledStartTime.getTime() - now.getTime()) / (1000 * 60);
      if (timeDifferenceMinutes < -1) {
        throw new ConflictException('Scheduled start time cannot be in the past');
      }

      // If scheduled for future, set status to scheduled
      if (timeDifferenceMinutes > 1) {
        initialStatus = 'scheduled';
      }
    }

    const production = new this.productionModel({
      ...createProductionDto,
      productionId,
      hostUserId: new Types.ObjectId(hostUserId),
      status: initialStatus,
      scheduledStartTime,
    });

    return production.save();
  }

  async findAll(): Promise<ProductionDocument[]> {
    return this.productionModel.find({ status: { $ne: 'cancelled' } })
      .populate('hostUserId', 'name email')
      .populate('participantUserIds', 'name email')
      .sort({ scheduledStartTime: 1, createdAt: 1 })
      .exec();
  }

  // Internal method that returns the actual Mongoose document
  private async findProductionDocument(productionId: string): Promise<ProductionDocument | null> {
    return this.productionModel.findOne({ productionId, status: { $ne: 'cancelled' } })
      .populate('hostUserId', 'name email')
      .populate('participantUserIds', 'name email')
      .exec();
  }

  // Public method that returns production for API responses
  async findByProductionId(productionId: string): Promise<ProductionDocument | null> {
    return this.findProductionDocument(productionId);
  }

  async findById(id: string): Promise<ProductionDocument> {
    const production = await this.productionModel.findById(id)
      .populate('hostUserId', 'name email')
      .populate('participantUserIds', 'name email')
      .exec();

    if (!production) {
      throw new NotFoundException('Production not found');
    }
    return production;
  }

  async findActiveByHostUserId(hostUserId: string): Promise<ProductionDocument | null> {
    // First check for live productions
    const liveProduction = await this.productionModel.findOne({
      hostUserId: new Types.ObjectId(hostUserId),
      status: 'live'
    })
      .populate('hostUserId', 'name email')
      .populate('participantUserIds', 'name email')
      .exec();

    if (liveProduction) {
      return liveProduction;
    }

    // If no live production, return the earliest scheduled production
    return this.productionModel.findOne({
      hostUserId: new Types.ObjectId(hostUserId),
      status: 'scheduled'
    })
      .sort({ scheduledStartTime: 1 }) // Earliest first
      .populate('hostUserId', 'name email')
      .populate('participantUserIds', 'name email')
      .exec();
  }

  async findByHostUserId(hostUserId: string, status?: string): Promise<ProductionDocument[]> {
    const query: any = {
      hostUserId: new Types.ObjectId(hostUserId),
      status: { $ne: 'cancelled' }
    };

    // If status is specified, filter by that status
    if (status) {
      query.status = status;
    }

    return this.productionModel.find(query)
      .populate('hostUserId', 'name email')
      .populate('participantUserIds', 'name email')
      .sort({ scheduledStartTime: 1, createdAt: -1 })
      .exec();
  }

  async findByParticipantUserId(participantUserId: string): Promise<ProductionDocument[]> {
    return this.productionModel.find({
      participantUserIds: new Types.ObjectId(participantUserId),
      status: { $in: ['scheduled', 'live'] }
    })
      .populate('hostUserId', 'name email')
      .populate('participantUserIds', 'name email')
      .exec();
  }

  async addParticipant(productionId: string, participantUserId: string, requestingUserId: string): Promise<ProductionDocument> {
    const production = await this.findProductionDocument(productionId);
    if (!production) {
      throw new NotFoundException('Production not found');
    }

    const participantObjectId = new Types.ObjectId(participantUserId);

    // Check if user is already a participant
    if (production.participantUserIds.some(id => id.equals(participantObjectId))) {
      return production; // Already a participant
    }

    // Add participant
    production.participantUserIds.push(participantObjectId);
    production.lastActivityAt = new Date();

    return production.save();
  }

  async removeParticipant(productionId: string, participantUserId: string): Promise<ProductionDocument> {
    const production = await this.findProductionDocument(productionId);
    if (!production) {
      throw new NotFoundException('Production not found');
    }

    const participantObjectId = new Types.ObjectId(participantUserId);
    production.participantUserIds = production.participantUserIds.filter(id => !id.equals(participantObjectId));
    production.lastActivityAt = new Date();

    return production.save();
  }

  async goLive(id: string, requestingUserId: string): Promise<ProductionDocument> {
    const production = await this.findById(id);

    // Only host can make production go live
    if (!production.hostUserId.equals(new Types.ObjectId(requestingUserId))) {
      throw new ForbiddenException('Only the host can make the production go live');
    }

    // Production must be scheduled
    if (production.status !== 'scheduled') {
      throw new ConflictException('Only scheduled productions can go live');
    }

    // Check if user has another live production and complete it
    const existingLiveProduction = await this.productionModel.findOne({
      hostUserId: new Types.ObjectId(requestingUserId),
      status: 'live'
    }).exec();

    if (existingLiveProduction && !(existingLiveProduction._id as any).equals(production._id)) {
      await this.completeProduction(existingLiveProduction.productionId, requestingUserId);
    }

    // Update production status
    production.status = 'live';
    production.wentLiveAt = new Date();

    return production.save();
  }

  async completeProduction(productionId: string, requestingUserId: string): Promise<ProductionDocument> {
    const production = await this.findProductionDocument(productionId);
    if (!production) {
      throw new NotFoundException('Production not found');
    }

    // Only host can complete production
    if (!production.hostUserId.equals(new Types.ObjectId(requestingUserId))) {
      throw new ForbiddenException('Only the host can complete the production');
    }

    production.status = 'completed';
    production.completedAt = new Date();
    production.streamEndedAt = new Date();
    production.isStreaming = false;

    return production.save();
  }

  // Alias for completeProduction to maintain API compatibility
  async complete(id: string, requestingUserId: string): Promise<ProductionDocument> {
    const production = await this.findById(id);
    return this.completeProduction(production.productionId, requestingUserId);
  }

  async update(productionId: string, updateProductionDto: UpdateProductionDto, requestingUserId: string): Promise<ProductionDocument> {
    const production = await this.findProductionDocument(productionId);
    if (!production) {
      throw new NotFoundException('Production not found');
    }

    // Only host can update production settings
    if (!production.hostUserId.equals(new Types.ObjectId(requestingUserId))) {
      throw new ForbiddenException('Only the host can update production settings');
    }

    // Cannot update productions that are live or completed (except for streaming settings)
    if (production.status === 'live' || production.status === 'completed') {
      // Allow only streaming-related updates for live productions
      const allowedFields = ['isStreaming', 'rtmpUrl', 'rtmpStreamKey', 'youtubeBroadcastId', 'youtubeStreamId'];
      const hasDisallowedUpdates = Object.keys(updateProductionDto).some(key => !allowedFields.includes(key));

      if (hasDisallowedUpdates && production.status === 'completed') {
        throw new ConflictException('Cannot update completed productions');
      }
      if (hasDisallowedUpdates && production.status === 'live') {
        throw new ConflictException('Can only update streaming settings for live productions');
      }
    }

    // If updating scheduled time, validate it's in the future (only for scheduled productions)
    if (updateProductionDto.scheduledStartTime && production.status === 'scheduled') {
      const newScheduledTime = new Date(updateProductionDto.scheduledStartTime);
      if (newScheduledTime <= new Date()) {
        throw new ConflictException('Scheduled start time must be in the future');
      }
    }

    Object.assign(production, updateProductionDto);
    production.lastActivityAt = new Date();

    return production.save();
  }

  async startStreaming(productionId: string, rtmpUrl: string, streamKey: string, requestingUserId: string): Promise<ProductionDocument> {
    const production = await this.update(productionId, {
      isStreaming: true,
      rtmpUrl,
      rtmpStreamKey: streamKey,
    }, requestingUserId);

    // Update stream started time if not already set
    if (!production.streamStartedAt) {
      production.streamStartedAt = new Date();
      await production.save();
    }

    return production;
  }

  async stopStreaming(productionId: string, requestingUserId: string): Promise<ProductionDocument> {
    return this.update(productionId, {
      isStreaming: false,
      rtmpUrl: undefined,
      rtmpStreamKey: undefined,
    }, requestingUserId);
  }

  async remove(productionId: string, requestingUserId: string): Promise<void> {
    const production = await this.findProductionDocument(productionId);
    if (!production) {
      throw new NotFoundException('Production not found');
    }

    // Only host can delete production
    if (!production.hostUserId.equals(new Types.ObjectId(requestingUserId))) {
      throw new ForbiddenException('Only the host can delete the production');
    }

    // Cannot delete live productions
    if (production.status === 'live') {
      throw new ConflictException('Cannot delete live productions. Complete the production first.');
    }

    production.status = 'cancelled';
    await production.save();
  }

  async createWithYouTube(
    createProductionDto: CreateProductionDto,
    hostUserId: string
  ): Promise<{ production: ProductionDocument; youtubeBroadcast?: any; youtubeStream?: any }> {
    // Create the production first
    const production = await this.create(createProductionDto, hostUserId);

    try {
      // Create YouTube broadcast
      const youtubeBroadcast = await this.youtubeService.createBroadcast(
        hostUserId,
        production.name,
        production.description || '',
        production.scheduledStartTime || new Date(),
        createProductionDto.youtubePrivacy
      );

      // Create YouTube stream
      const youtubeStream = await this.youtubeService.createStream(
        hostUserId,
        `${production.name} - Stream`,
        production.description || '',
        '720p'
      );

      // Bind broadcast to stream
      await this.youtubeService.bindBroadcastToStream(
        hostUserId,
        youtubeBroadcast.id,
        youtubeStream.id
      );

      // Update production with YouTube details
      production.youtubeBroadcastId = youtubeBroadcast.id;
      production.youtubeStreamId = youtubeStream.id;
      await production.save();

      return { production, youtubeBroadcast, youtubeStream };
    } catch (error) {
      console.error('Failed to create YouTube broadcast/stream:', error);
      // Return production without YouTube integration
      return { production };
    }
  }
}
