import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ProductionDocument = Production & Document;

@Schema({ timestamps: true })
export class Production {
  @Prop({ required: true, unique: true })
  productionId: string; // The 6-character production ID

  @Prop({ required: true })
  name: string;

  @Prop()
  description?: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  hostUserId: Types.ObjectId;

  @Prop({ type: [{ type: Types.ObjectId, ref: 'User' }], default: [] })
  participantUserIds: Types.ObjectId[];

  // Scheduling fields (from Event schema)
  @Prop()
  scheduledStartTime?: Date;

  @Prop()
  wentLiveAt?: Date;

  @Prop()
  completedAt?: Date;

  // Streaming fields
  @Prop({ default: false })
  isStreaming: boolean;

  @Prop()
  rtmpUrl?: string;

  @Prop()
  rtmpStreamKey?: string;

  @Prop()
  streamStartedAt?: Date;

  @Prop()
  streamEndedAt?: Date;

  // Unified status: scheduled -> live -> completed/cancelled
  @Prop({ default: 'scheduled' })
  status: 'scheduled' | 'live' | 'completed' | 'cancelled';

  @Prop({ default: Date.now })
  lastActivityAt: Date;

  // YouTube integration
  @Prop()
  youtubeBroadcastId?: string;

  @Prop()
  youtubeStreamId?: string;

  @Prop({ default: 'unlisted' })
  youtubePrivacy?: 'public' | 'unlisted' | 'private';
}

export const ProductionSchema = SchemaFactory.createForClass(Production);

// Create indexes for efficient lookups
// Note: productionId index is created automatically by unique: true
ProductionSchema.index({ hostUserId: 1 });
ProductionSchema.index({ participantUserIds: 1 });
ProductionSchema.index({ status: 1, lastActivityAt: 1 });
ProductionSchema.index({ hostUserId: 1, status: 1 }); // For finding user's active production
ProductionSchema.index({ status: 1 });
ProductionSchema.index({ scheduledStartTime: 1 });
ProductionSchema.index({ hostUserId: 1, scheduledStartTime: 1 });
