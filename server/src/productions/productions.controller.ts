import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, Query } from '@nestjs/common';
import { ProductionsService } from './productions.service';
import { CreateProductionDto } from './dto/create-production.dto';
import { UpdateProductionDto } from './dto/update-production.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('productions')
export class ProductionsController {
  constructor(private readonly productionsService: ProductionsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  create(@Body() createProductionDto: CreateProductionDto, @Request() req) {
    return this.productionsService.create(createProductionDto, req.user.userId);
  }

  @Post('with-youtube')
  @UseGuards(JwtAuthGuard)
  createWithYouTube(@Body() createProductionDto: CreateProductionDto, @Request() req) {
    return this.productionsService.createWithYouTube(createProductionDto, req.user.userId);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  findAll(@Query('my') my?: string, @Request() req?) {
    if (my === 'true') {
      return this.productionsService.findByHostUserId(req.user.userId);
    }
    return this.productionsService.findAll();
  }

  @Get('my')
  @UseGuards(JwtAuthGuard)
  findMy(@Request() req, @Query('status') status?: string) {
    return this.productionsService.findByHostUserId(req.user.userId, status);
  }

  @Get('my-active')
  @UseGuards(JwtAuthGuard)
  findMyActive(@Request() req) {
    return this.productionsService.findActiveByHostUserId(req.user.userId);
  }

  @Get('my-history')
  @UseGuards(JwtAuthGuard)
  findMyHistory(@Request() req) {
    return this.productionsService.findByHostUserId(req.user.userId);
  }

  @Get(':productionId')
  findOne(@Param('productionId') productionId: string) {
    return this.productionsService.findByProductionId(productionId);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  update(@Param('id') id: string, @Body() updateProductionDto: UpdateProductionDto, @Request() req) {
    // Support both MongoDB ObjectId and productionId
    if (id.length === 6) {
      return this.productionsService.update(id, updateProductionDto, req.user.userId);
    } else {
      // Find by MongoDB ID first, then update using productionId
      return this.productionsService.findById(id).then(production =>
        this.productionsService.update(production.productionId, updateProductionDto, req.user.userId)
      );
    }
  }

  @Post(':productionId/complete')
  @UseGuards(JwtAuthGuard)
  completeProduction(@Param('productionId') productionId: string, @Request() req) {
    return this.productionsService.completeProduction(productionId, req.user.userId);
  }

  @Post(':id/go-live')
  @UseGuards(JwtAuthGuard)
  goLive(@Param('id') id: string, @Request() req) {
    return this.productionsService.goLive(id, req.user.userId);
  }

  @Post(':id/complete')
  @UseGuards(JwtAuthGuard)
  complete(@Param('id') id: string, @Request() req) {
    return this.productionsService.complete(id, req.user.userId);
  }

  @Patch(':productionId')
  @UseGuards(JwtAuthGuard)
  updateByProductionId(@Param('productionId') productionId: string, @Body() updateProductionDto: UpdateProductionDto, @Request() req) {
    return this.productionsService.update(productionId, updateProductionDto, req.user.userId);
  }

  @Post(':productionId/join')
  @UseGuards(JwtAuthGuard)
  joinProduction(@Param('productionId') productionId: string, @Request() req) {
    return this.productionsService.addParticipant(productionId, req.user.userId, req.user.userId);
  }

  @Post(':productionId/leave')
  @UseGuards(JwtAuthGuard)
  leaveProduction(@Param('productionId') productionId: string, @Request() req) {
    return this.productionsService.removeParticipant(productionId, req.user.userId);
  }

  @Post(':productionId/start-stream')
  @UseGuards(JwtAuthGuard)
  startStream(
    @Param('productionId') productionId: string,
    @Body() body: { rtmpUrl: string; streamKey: string },
    @Request() req
  ) {
    return this.productionsService.startStreaming(productionId, body.rtmpUrl, body.streamKey, req.user.userId);
  }

  @Post(':productionId/stop-stream')
  @UseGuards(JwtAuthGuard)
  stopStream(@Param('productionId') productionId: string, @Request() req) {
    return this.productionsService.stopStreaming(productionId, req.user.userId);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  remove(@Param('id') id: string, @Request() req) {
    // Support both MongoDB ObjectId and productionId
    if (id.length === 6) {
      return this.productionsService.remove(id, req.user.userId);
    } else {
      // Find by MongoDB ID first, then remove using productionId
      return this.productionsService.findById(id).then(production =>
        this.productionsService.remove(production.productionId, req.user.userId)
      );
    }
  }

  @Delete(':productionId')
  @UseGuards(JwtAuthGuard)
  removeByProductionId(@Param('productionId') productionId: string, @Request() req) {
    return this.productionsService.remove(productionId, req.user.userId);
  }
}
