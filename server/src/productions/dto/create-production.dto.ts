import { IsString, <PERSON><PERSON>ptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>th, IsDateString, IsE<PERSON> } from 'class-validator';

export class CreateProductionDto {
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name: string;

  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @IsOptional()
  @IsString()
  @MinLength(6)
  @MaxLength(6)
  productionId?: string; // If not provided, will be auto-generated

  @IsOptional()
  @IsDateString()
  scheduledStartTime?: string; // If provided, production starts as 'scheduled', otherwise 'live'

  @IsOptional()
  @IsEnum(['public', 'unlisted', 'private'])
  youtubePrivacy?: 'public' | 'unlisted' | 'private' = 'unlisted';
}
