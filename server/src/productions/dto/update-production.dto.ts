import { PartialType } from '@nestjs/mapped-types';
import { CreateProductionDto } from './create-production.dto';
import { IsOptional, IsBoolean, IsString, IsArray, IsMongoId, IsEnum } from 'class-validator';
import { Types } from 'mongoose';

export class UpdateProductionDto extends PartialType(CreateProductionDto) {
  @IsOptional()
  @IsBoolean()
  isStreaming?: boolean;

  @IsOptional()
  @IsString()
  rtmpUrl?: string;

  @IsOptional()
  @IsString()
  rtmpStreamKey?: string;

  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  participantUserIds?: Types.ObjectId[];

  @IsOptional()
  @IsEnum(['scheduled', 'live', 'completed', 'cancelled'])
  status?: 'scheduled' | 'live' | 'completed' | 'cancelled';

  @IsOptional()
  @IsString()
  youtubeBroadcastId?: string;

  @IsOptional()
  @IsString()
  youtubeStreamId?: string;
}
