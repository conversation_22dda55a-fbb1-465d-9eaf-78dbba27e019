import { Controller, Get } from '@nestjs/common';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHealth() {
    return this.appService.getHealth();
  }

  @Get('health')
  getHealthCheck() {
    return this.appService.getHealth();
  }

  @Get('api/config')
  getConfig() {
    return this.appService.getConfig();
  }
}
