# Production Environment Outputs

output "signaling_server_url" {
  description = "URL of the signaling server"
  value       = module.sai_platform.signaling_server_url
}

output "media_server_url" {
  description = "URL of the media server"
  value       = module.sai_platform.media_server_url
}

output "turn_server_ip" {
  description = "External IP address of the TURN server"
  value       = module.sai_platform.turn_server_ip
}

output "monitoring_dashboard_url" {
  description = "URL of the monitoring dashboard"
  value       = module.sai_platform.monitoring_dashboard_url
}

output "dns_configuration" {
  description = "DNS configuration for custom domain"
  value       = module.sai_platform.dns_configuration
}

output "environment_config" {
  description = "Environment configuration for application deployment"
  value       = module.sai_platform.environment_config
  sensitive   = true
}

output "deployment_commands" {
  description = "Commands to deploy applications to the infrastructure"
  sensitive   = true
  value       = module.sai_platform.deployment_commands
}

output "security_info" {
  description = "Security configuration information"
  value       = module.sai_platform.security_info
}
