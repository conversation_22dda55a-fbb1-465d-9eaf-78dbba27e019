# Local Development Environment Variables

variable "project_id" {
  description = "Google Cloud Project ID"
  type        = string
}

variable "region" {
  description = "Primary deployment region"
  type        = string
  default     = "us-central1"
}

variable "app_name" {
  description = "Application name prefix"
  type        = string
  default     = "sai-platform"
}

# MongoDB Atlas Configuration
variable "atlas_org_id" {
  description = "MongoDB Atlas Organization ID"
  type        = string
}

variable "database_name" {
  description = "Name of the MongoDB database"
  type        = string
  default     = "switcher-ai"
}

variable "db_username" {
  description = "MongoDB database username"
  type        = string
  default     = "app-user"
}

variable "atlas_region" {
  description = "MongoDB Atlas region"
  type        = string
  default     = "US_CENTRAL1"
}





# Server Environment Variables
variable "jwt_secret" {
  description = "JWT secret for token signing"
  type        = string
  default     = "your-super-secret-jwt-key-for-development"
  sensitive   = true
}

variable "google_callback_url" {
  description = "Google OAuth callback URL"
  type        = string
  default     = "http://localhost:3001/auth/google/callback"
}

variable "facebook_app_id" {
  description = "Facebook App ID (optional)"
  type        = string
  default     = ""
}

variable "facebook_app_secret" {
  description = "Facebook App Secret (optional)"
  type        = string
  default     = ""
  sensitive   = true
}

variable "facebook_callback_url" {
  description = "Facebook OAuth callback URL"
  type        = string
  default     = "http://localhost:3001/auth/facebook/callback"
}

variable "frontend_url" {
  description = "Frontend URL for OAuth redirects"
  type        = string
  default     = "http://localhost:5173"
}

variable "media_server_url" {
  description = "Media server URL"
  type        = string
  default     = "http://localhost:8081"
}

variable "rtmp_service_url" {
  description = "RTMP service URL"
  type        = string
  default     = "http://localhost:3002"
}

variable "server_port" {
  description = "Server port"
  type        = string
  default     = "3001"
}
