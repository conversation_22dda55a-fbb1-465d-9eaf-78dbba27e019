# Local Development Environment Configuration
# This environment only manages OAuth client configuration for local development

terraform {
  required_version = ">= 1.0"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    local = {
      source  = "hashicorp/local"
      version = "~> 2.0"
    }
    mongodbatlas = {
      source  = "mongodb/mongodbatlas"
      version = "~> 1.15"
    }
  }

  # Optional: Configure remote state backend for local environment
  # backend "gcs" {
  #   bucket = "your-terraform-state-bucket"
  #   prefix = "sai-platform/local"
  # }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

# Local values for local environment
locals {
  environment = "local"
  name_prefix = "${var.app_name}-${local.environment}"
  common_labels = {
    project     = var.app_name
    environment = local.environment
    managed-by  = "terraform"
    region      = var.region
  }
}

# Local development environment - no OAuth infrastructure managed here
# OAuth credentials should be configured manually in Google Cloud Console

# MongoDB Atlas Database Module
module "mongodb" {
  source = "../../modules/mongodb"

  project_id    = var.project_id
  environment   = local.environment
  name_prefix   = local.name_prefix
  atlas_org_id  = var.atlas_org_id
  database_name = var.database_name
  db_username   = var.db_username
  atlas_region  = var.atlas_region

  labels = local.common_labels
}


