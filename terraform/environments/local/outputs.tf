# Local Development Environment Outputs

output "database_name" {
  description = "The name of the MongoDB database"
  value       = module.mongodb.database_name
}

output "mongodb_connection_string" {
  description = "MongoDB connection string for Mongoose"
  value       = module.mongodb.connection_string
  sensitive   = true
}

output "mongodb_username" {
  description = "MongoDB database username"
  value       = module.mongodb.database_username
}

output "mongodb_password" {
  description = "MongoDB database password"
  value       = module.mongodb.database_password
  sensitive   = true
}

output "mongodb_config_file" {
  description = "Path to the MongoDB configuration file"
  value       = module.mongodb.config_file
}


