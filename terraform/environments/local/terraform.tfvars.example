# Local Development Environment Configuration Example
# Copy this file to terraform.tfvars and fill in your actual values

# Project Configuration
project_id = "switcher-ai-469818"
region     = "us-central1"

# Application Configuration
app_name = "Switcher AI"

# MongoDB Atlas Configuration
atlas_org_id = "68adf07443f283067e8de755"  # Replace with your MongoDB Atlas Organization ID
database_name = "switcher-ai"
db_username = "app-user"
atlas_region = "CENTRAL_US"

# Note: OAuth configuration is managed manually through Google Cloud Console
# No OAuth infrastructure is managed by this Terraform configuration
