# Development Environment Variables

variable "project_id" {
  description = "Google Cloud Project ID"
  type        = string
}

variable "region" {
  description = "Primary deployment region"
  type        = string
  default     = "us-central1"
}

variable "zone" {
  description = "Primary deployment zone"
  type        = string
  default     = "us-central1-a"
}

variable "app_name" {
  description = "Application name prefix"
  type        = string
  default     = "webrtc-platform"
}

variable "domain_name" {
  description = "Custom domain name for the application (optional)"
  type        = string
  default     = ""
}

variable "signaling_server_image" {
  description = "Docker image for the signaling server"
  type        = string
  default     = "gcr.io/PROJECT_ID/sai-signaling:latest"
}

variable "media_server_image" {
  description = "Docker image for the media server"
  type        = string
  default     = "gcr.io/PROJECT_ID/media-server:latest"
}

variable "turn_server_image" {
  description = "Docker image for the TURN server"
  type        = string
  default     = "coturn/coturn:latest"
}

variable "turn_username" {
  description = "TURN server username"
  type        = string
  default     = "webrtc"
  sensitive   = true
}

variable "turn_password" {
  description = "TURN server password"
  type        = string
  default     = "webrtc123"
  sensitive   = true
}

variable "notification_email" {
  description = "Email address for monitoring notifications"
  type        = string
  default     = ""
}
