# Main Terraform configuration for WebRTC Streaming Platform

# Local values for resource naming and tagging
locals {
  name_prefix = "${var.app_name}-${var.environment}"
  common_labels = merge(var.labels, {
    environment = var.environment
    region      = var.region
  })
}

# Enable required Google Cloud APIs
resource "google_project_service" "required_apis" {
  for_each = toset([
    "compute.googleapis.com",
    "run.googleapis.com",
    "artifactregistry.googleapis.com",
    "cloudbuild.googleapis.com",
    "logging.googleapis.com",
    "monitoring.googleapis.com",
    "secretmanager.googleapis.com",
    "dns.googleapis.com",
    "vpcaccess.googleapis.com"
  ])

  service = each.value
  project = var.project_id

  disable_dependent_services = false
  disable_on_destroy         = false
}

# Networking Module
module "networking" {
  source = "./modules/networking"

  project_id              = var.project_id
  region                  = var.region
  vpc_name                = "${local.name_prefix}-vpc"
  subnet_name             = "${local.name_prefix}-subnet"
  subnet_cidr             = var.subnet_cidr
  allowed_source_ranges   = var.allowed_source_ranges
  labels                  = local.common_labels

  depends_on = [google_project_service.required_apis]
}

# Security Module (IAM, Service Accounts, Secrets)
module "security" {
  source = "./modules/security"

  project_id    = var.project_id
  name_prefix   = local.name_prefix
  turn_username = var.turn_username
  turn_password = var.turn_password
  labels        = local.common_labels

  depends_on = [google_project_service.required_apis]
}

# Artifact Registry Module
module "registry" {
  source = "./modules/registry"

  project_id    = var.project_id
  region        = var.region
  repository_id = "${local.name_prefix}-repo"
  labels        = local.common_labels

  depends_on = [google_project_service.required_apis]
}

# Signaling Server (Cloud Run)
module "signaling_server" {
  source = "./modules/cloud-run"

  project_id                    = var.project_id
  region                        = var.region
  environment                   = var.environment
  name_prefix                   = local.name_prefix
  vpc_name                      = module.networking.vpc_name
  subnet_name                   = module.networking.subnet_name
  signaling_server_image        = replace(var.signaling_server_image, "PROJECT_ID", var.project_id)
  media_server_image            = "placeholder"  # Not used for signaling-only
  cpu                           = var.cloud_run_cpu
  memory                        = var.cloud_run_memory
  max_instances                 = var.cloud_run_max_instances
  signaling_service_account     = module.security.signaling_service_account_email
  media_service_account         = module.security.media_service_account_email
  turn_server_ip                = module.compute.turn_server_ip
  turn_username                 = var.turn_username
  turn_password_secret          = module.security.turn_password_secret_id
  media_server_url              = module.media_server.media_server_url
  labels                        = local.common_labels

  depends_on = [
    module.networking,
    module.security,
    module.compute,
    module.media_server
  ]
}

# Media Server (Compute Engine VM with npm package)
module "media_server" {
  source = "./modules/media-server-vm"

  project_id            = var.project_id
  region                = var.region
  zone                  = var.zone
  name_prefix           = local.name_prefix
  vpc_name              = module.networking.vpc_name
  subnet_name           = module.networking.subnet_name
  machine_type          = var.media_server_machine_type
  service_account_email = module.security.media_service_account_email
  package_name          = "@sai/media-server"
  package_version       = "1.0.0"
  labels                = local.common_labels

  depends_on = [
    module.networking,
    module.security
  ]
}

# Compute Engine Module (TURN Server)
module "compute" {
  source = "./modules/compute"

  project_id                = var.project_id
  region                    = var.region
  zone                      = var.zone
  name_prefix               = local.name_prefix
  vpc_name                  = module.networking.vpc_name
  subnet_name               = module.networking.subnet_name
  machine_type              = var.turn_server_machine_type
  turn_server_image         = var.turn_server_image
  turn_username             = var.turn_username
  turn_password_secret      = module.security.turn_password_secret_id
  service_account_email     = module.security.turn_service_account_email
  labels                    = local.common_labels

  depends_on = [
    module.networking,
    module.security
  ]
}

# Monitoring Module (optional)
module "monitoring" {
  count  = var.enable_monitoring ? 1 : 0
  source = "./modules/monitoring"

  project_id                = var.project_id
  name_prefix               = local.name_prefix
  notification_email        = var.notification_email
  signaling_service_name    = module.signaling_server.signaling_service_name
  media_service_name        = module.media_server.instance_name
  turn_instance_name        = module.compute.turn_instance_name
  labels                    = local.common_labels

  depends_on = [
    module.signaling_server,
    module.media_server,
    module.compute
  ]
}
