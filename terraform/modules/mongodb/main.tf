# MongoDB Module for MongoDB Atlas
# Creates a MongoDB Atlas cluster for use with NestJS and Mongoose

terraform {
  required_providers {
    mongodbatlas = {
      source  = "mongodb/mongodbatlas"
      version = "~> 1.15"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }
}

# Create a sanitized name for resources (alphanumeric and hyphens only)
locals {
  sanitized_name_prefix = replace(replace(lower(var.name_prefix), " ", "-"), "_", "-")
}

# Create a MongoDB Atlas project
resource "mongodbatlas_project" "project" {
  name   = "${local.sanitized_name_prefix}-${var.environment}"
  org_id = var.atlas_org_id

  is_collect_database_specifics_statistics_enabled = true
  is_data_explorer_enabled                         = true
  is_performance_advisor_enabled                   = true
  is_realtime_performance_panel_enabled            = true
  is_schema_advisor_enabled                        = true
}

# Create a database user
resource "random_password" "db_password" {
  length  = 16
  special = true
}

resource "mongodbatlas_database_user" "user" {
  username           = var.db_username
  password           = random_password.db_password.result
  project_id         = mongodbatlas_project.project.id
  auth_database_name = "admin"

  roles {
    role_name     = "readWrite"
    database_name = var.database_name
  }

  # Add additional role for admin operations if needed
  roles {
    role_name     = "dbAdmin"
    database_name = var.database_name
  }
}



# Create a MongoDB Atlas cluster
resource "mongodbatlas_cluster" "cluster" {
  project_id   = mongodbatlas_project.project.id
  name         = "${local.sanitized_name_prefix}-${var.environment}-cluster"

  # Use M0 (free tier) for local development
  cluster_type               = "REPLICASET"
  provider_name              = "TENANT"
  backing_provider_name      = "GCP"
  provider_region_name       = var.atlas_region
  provider_instance_size_name = "M0"
}

# Create network access (allow access from anywhere for local development)
resource "mongodbatlas_project_ip_access_list" "access_list" {
  project_id = mongodbatlas_project.project.id
  cidr_block = "0.0.0.0/0"
  comment    = "Allow access from anywhere for local development"
}

# Store database credentials in a local file for development
resource "local_file" "mongodb_config" {
  content = jsonencode({
    connection_string = replace(
      mongodbatlas_cluster.cluster.connection_strings[0].standard_srv,
      "mongodb+srv://",
      "mongodb+srv://${mongodbatlas_database_user.user.username}:${urlencode(random_password.db_password.result)}@"
    )
    username = mongodbatlas_database_user.user.username
    password = random_password.db_password.result
    database = var.database_name
    cluster_name = mongodbatlas_cluster.cluster.name
  })
  filename = "${path.root}/../../server/mongodb-config.json"

  file_permission = "0600"
}
