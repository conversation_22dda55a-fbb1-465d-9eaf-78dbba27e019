# MongoDB Atlas Module Variables

variable "project_id" {
  description = "Google Cloud Project ID (for naming consistency)"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod, local)"
  type        = string
}

variable "name_prefix" {
  description = "Prefix for resource names"
  type        = string
}

variable "atlas_org_id" {
  description = "MongoDB Atlas Organization ID"
  type        = string
}

variable "database_name" {
  description = "Name of the MongoDB database"
  type        = string
  default     = "switcher-ai"
}

variable "db_username" {
  description = "MongoDB database username"
  type        = string
  default     = "app-user"
}

variable "atlas_region" {
  description = "MongoDB Atlas region"
  type        = string
  default     = "CENTRAL_US"
}

variable "labels" {
  description = "Labels to apply to resources"
  type        = map(string)
  default     = {}
}
