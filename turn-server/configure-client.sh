#!/bin/bash

# Configure WebRTC client to use deployed TURN server
# This script updates the .env file with the TURN server IP

set -e

# Configuration
PROJECT_ID=${GOOGLE_CLOUD_PROJECT:-"switcher-ai-469818"}
ZONE=${ZONE:-"us-central1-a"}
INSTANCE_NAME="coturn-turn-server"

echo "🔧 Configuring WebRTC client for deployed TURN server..."

# Get the external IP of the TURN server instance
if ! gcloud compute instances describe ${INSTANCE_NAME} --zone=${ZONE} --project=${PROJECT_ID} &>/dev/null; then
    echo "❌ Error: TURN server instance '${INSTANCE_NAME}' not found."
    echo "   Make sure you've deployed the TURN server first with:"
    echo "   cd turn-server && ./deploy-gce.sh"
    exit 1
fi

EXTERNAL_IP=$(gcloud compute instances describe ${INSTANCE_NAME} \
    --zone=${ZONE} \
    --format='get(networkInterfaces[0].accessConfigs[0].natIP)' \
    --project=${PROJECT_ID})

if [ -z "$EXTERNAL_IP" ]; then
    echo "❌ Error: Could not get external IP for instance '${INSTANCE_NAME}'"
    exit 1
fi

echo "📍 Found TURN server at: ${EXTERNAL_IP}"

# Navigate to project root
cd "$(dirname "$0")/.."

# Create or update .env file
if [ -f .env ]; then
    echo "📝 Updating existing .env file..."
    # Remove existing TURN server configuration
    sed -i.bak '/^VITE_TURN_/d' .env
else
    echo "📝 Creating new .env file..."
    cp .env.example .env
fi

# Add TURN server configuration
cat >> .env << EOF

# TURN Server Configuration (auto-generated)
VITE_TURN_SERVER=${EXTERNAL_IP}
VITE_TURN_USERNAME=webrtc
VITE_TURN_PASSWORD=webrtc123
EOF

echo "✅ Configuration updated!"
echo ""
echo "📋 TURN Server Details:"
echo "   IP: ${EXTERNAL_IP}"
echo "   Port: 3478"
echo "   Username: webrtc"
echo "   Password: webrtc123"
echo ""
echo "🔄 Next steps:"
echo "   1. Restart your development server: npm run dev"
echo "   2. Or rebuild for production: npm run build"
echo ""
echo "🧪 Test the TURN server:"
echo "   You can test the connection by opening the browser developer console"
echo "   and looking for TURN server connection logs when joining a room."
