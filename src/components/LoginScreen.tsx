import React, { useState } from 'react';
import { LogIn, UserPlus, Mail, Lock, User, Podcast as Broadcast, Users, ArrowRight } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

interface LoginScreenProps {
  onJoinAsParticipant?: (productionId: string) => void;
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ onJoinAsParticipant }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [joinProductionId, setJoinProductionId] = useState('');
  const [joinError, setJoinError] = useState('');

  const { login, register } = useAuth();

  // Check for production ID in URL path and pre-populate
  React.useEffect(() => {
    const pathMatch = window.location.pathname.match(/^\/join\/(.+)$/);
    if (pathMatch && pathMatch[1]) {
      setJoinProductionId(pathMatch[1].toUpperCase());
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      if (isLogin) {
        await login(email, password);
      } else {
        await register(email, password, name);
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Authentication failed');
    } finally {
      setLoading(false);
    }
  };

  const handleOAuthLogin = (provider: 'google' | 'facebook') => {
    window.location.href = `${API_BASE_URL}/auth/${provider}`;
  };

  const handleJoinProduction = async () => {
    if (!joinProductionId.trim()) {
      setJoinError('Please enter a production ID');
      return;
    }

    if (!onJoinAsParticipant) {
      setJoinError('Join functionality not available');
      return;
    }

    setJoinError('');

    try {
      // Check if production exists
      const response = await axios.get(`${API_BASE_URL}/productions/${joinProductionId.trim().toUpperCase()}`);
      if (response.data) {
        onJoinAsParticipant(joinProductionId.trim().toUpperCase());
      }
    } catch (err: any) {
      setJoinError(err.response?.data?.message || 'Production not found');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4" style={{ backgroundColor: '#1a1f2e' }}>
      <div className="max-w-6xl w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            switcher<span className="text-orange-500">.ai</span>
          </h1>
          <p className="text-gray-400 text-lg">
            Fully automated livestreaming
          </p>
        </div>

        <div className={`grid gap-8 ${onJoinAsParticipant ? 'md:grid-cols-2' : 'md:grid-cols-1 max-w-md mx-auto'}`}>
          {/* Authentication Column */}
          <div>
            {/* Auth Form */}
            <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Get Started</h2>

              {error && (
                <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3 text-red-200 text-sm mb-6">
                  {error}
                </div>
              )}

              {/* Social Sign-In (Priority) */}
              <div className="space-y-3 mb-6">

                <button
                  onClick={() => handleOAuthLogin('google')}
                  className="w-full bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-3 shadow-lg"
                >
              <svg className="w-6 h-6" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span>Continue with Google</span>
            </button>

                <button
                  onClick={() => handleOAuthLogin('facebook')}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-xl transition-colors duration-200 flex items-center justify-center space-x-3 shadow-lg"
                >
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
              <span>Continue with Facebook</span>
            </button>
          </div>

              {/* Divider */}
              <div className="relative mb-4">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-600"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-4 bg-gray-800 text-gray-400">Or use email and password</span>
                </div>
              </div>

              {/* Email/Password Form (Secondary) */}
              <form onSubmit={handleSubmit} className="space-y-3">
            {!isLogin && (
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Full Name
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="w-full bg-gray-700/80 border border-gray-600/50 rounded-xl pl-10 pr-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
                    placeholder="Enter your full name"
                    required={!isLogin}
                  />
                </div>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Email Address
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full bg-gray-700/80 border border-gray-600/50 rounded-xl pl-10 pr-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full bg-gray-700/80 border border-gray-600/50 rounded-xl pl-10 pr-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20"
                  placeholder="Enter your password"
                  required
                  minLength={6}
                />
              </div>
            </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-gray-600/80 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed mt-4"
                >
                  {loading ? (
                    <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <span>Continue</span>
                  )}
                </button>
              </form>

              {/* Toggle Login/Register */}
              <div className="mt-4 text-center">
                <button
                  onClick={() => setIsLogin(!isLogin)}
                  className="text-orange-400 hover:text-orange-300 text-sm transition-colors"
                >
                  {isLogin ? "Don't have an account? Sign up" : "Already have an account? Sign in"}
                </button>
              </div>
            </div>
          </div>

          {/* Join Production Section */}
          {onJoinAsParticipant && (
            <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Join Production</h2>
              <p className="text-gray-400 text-sm mb-6">
                Have a production ID? Join as a participant without signing in
              </p>

              {joinError && (
                <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3 text-red-200 text-sm mb-4">
                  {joinError}
                </div>
              )}

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Production ID
                  </label>
                  <input
                    type="text"
                    placeholder="Enter Production ID"
                    value={joinProductionId}
                    onChange={(e) => setJoinProductionId(e.target.value.toUpperCase())}
                    className="w-full bg-gray-700/80 border border-gray-600/50 rounded-xl px-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-500/20 text-center text-lg font-mono tracking-wider"
                    maxLength={6}
                  />
                </div>
                <button
                  onClick={handleJoinProduction}
                  disabled={!joinProductionId.trim()}
                  className="w-full bg-gray-600/80 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span>Join Production</span>
                  <ArrowRight className="w-5 h-5" />
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
