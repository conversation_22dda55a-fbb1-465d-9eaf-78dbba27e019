import React, { useState, useEffect } from 'react';
import { X, Check, AlertCircle, ExternalLink, Unlink } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

interface PlatformStatus {
  connected: boolean;
  linkedAt: string | null;
  hasValidToken: boolean;
}

interface PlatformData {
  hasPassword: boolean;
  platforms: {
    google: PlatformStatus;
    facebook: PlatformStatus;
  };
}

interface LinkedPlatformsDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const LinkedPlatformsDialog: React.FC<LinkedPlatformsDialogProps> = ({
  isOpen,
  onClose,
}) => {
  const { refreshUser } = useAuth();
  const [platformData, setPlatformData] = useState<PlatformData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [unlinkingPlatform, setUnlinkingPlatform] = useState<string | null>(null);
  const [linkingPlatform, setLinkingPlatform] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      fetchPlatformStatus();
    }
  }, [isOpen]);

  const fetchPlatformStatus = async (showLoading = true) => {
    try {
      if (showLoading) {
        setLoading(true);
      }
      setError('');
      const response = await axios.get(`${API_BASE_URL}/auth/platforms`);
      setPlatformData(response.data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to load platform status');
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  };

  const handleLinkPlatform = (platform: 'google' | 'facebook') => {
    // Set linking state to show loading on the specific platform button
    setLinkingPlatform(platform);
    setError('');

    // Open OAuth flow in a new window
    const width = 500;
    const height = 600;
    const left = window.screenX + (window.outerWidth - width) / 2;
    const top = window.screenY + (window.outerHeight - height) / 2;

    const popup = window.open(
      `${API_BASE_URL}/auth/${platform}/link`,
      'oauth',
      `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
    );

    let hasHandledSuccess = false; // Flag to prevent double refresh

    // Listen for messages from the popup
    const handleMessage = async (event: MessageEvent) => {
      // Verify origin for security - should be from our backend server
      const expectedOrigin = API_BASE_URL.includes('localhost') ? 'http://localhost:3001' : API_BASE_URL;
      if (event.origin !== expectedOrigin) {
        return;
      }

      if (event.data.type === 'OAUTH_SUCCESS' && event.data.provider === platform) {
        if (hasHandledSuccess) return; // Prevent double handling
        hasHandledSuccess = true;

        // Success - refresh platform status only (no full user refresh)
        fetchPlatformStatus(false); // Don't show loading spinner for background refresh
        setLinkingPlatform(null); // Clear linking state

        // Clean up
        window.removeEventListener('message', handleMessage);
        if (popup && !popup.closed) {
          popup.close();
        }
      } else if (event.data.type === 'OAUTH_ERROR' && event.data.provider === platform) {
        // Error - show error message
        setError(event.data.error || `Failed to link ${platform}`);
        setLinkingPlatform(null); // Clear linking state

        // Clean up
        window.removeEventListener('message', handleMessage);
        if (popup && !popup.closed) {
          popup.close();
        }
      }
    };

    // Add message listener
    window.addEventListener('message', handleMessage);

    // Fallback: Listen for the popup to close (in case postMessage fails)
    const checkClosed = setInterval(() => {
      if (popup?.closed) {
        clearInterval(checkClosed);
        window.removeEventListener('message', handleMessage);

        // Only refresh if we haven't already handled success via postMessage
        if (!hasHandledSuccess) {
          setTimeout(() => {
            fetchPlatformStatus(false); // Don't show loading spinner for background refresh
            setLinkingPlatform(null); // Clear linking state
          }, 1000);
        }
      }
    }, 1000);

    // Clean up if popup is blocked or fails to open
    if (!popup) {
      window.removeEventListener('message', handleMessage);
      setError('Popup blocked. Please allow popups for this site.');
      setLinkingPlatform(null); // Clear linking state
    }
  };

  const handleUnlinkPlatform = async (platform: 'google' | 'facebook') => {
    if (!platformData) return;

    // Check if this would leave the user without any authentication method
    const otherPlatform = platform === 'google' ? 'facebook' : 'google';
    const hasOtherPlatform = platformData.platforms[otherPlatform].connected;
    
    if (!platformData.hasPassword && !hasOtherPlatform) {
      setError('Cannot unlink the only authentication method. Please set a password first.');
      return;
    }

    try {
      setUnlinkingPlatform(platform);
      setError('');
      await axios.post(`${API_BASE_URL}/auth/platforms/${platform}/unlink`);
      await fetchPlatformStatus(false); // Don't show loading spinner for background refresh
      // Only refresh user context if this was the last authentication method
      // (the backend will handle this validation)
    } catch (err: any) {
      setError(err.response?.data?.message || `Failed to unlink ${platform}`);
    } finally {
      setUnlinkingPlatform(null);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Unknown';
    return new Date(dateString).toLocaleDateString();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 border border-gray-600 rounded-2xl w-full max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-600">
          <h2 className="text-xl font-bold text-white">Linked Platforms</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="w-8 h-8 border-4 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : error ? (
            <div className="flex items-center space-x-2 text-red-400 bg-red-400/10 border border-red-400/20 rounded-lg p-3 mb-4">
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
              <span className="text-sm">{error}</span>
            </div>
          ) : platformData ? (
            <div className="space-y-4">
              {/* Google Platform */}
              <div className="bg-gray-700/50 border border-gray-600/50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">G</span>
                    </div>
                    <div>
                      <h3 className="text-white font-medium">Google</h3>
                      <p className="text-gray-400 text-sm">
                        {platformData.platforms.google.connected ? 'Connected' : 'Not connected'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {platformData.platforms.google.connected ? (
                      <Check className="w-5 h-5 text-green-400" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-gray-400" />
                    )}
                  </div>
                </div>
                
                {platformData.platforms.google.connected && (
                  <div className="text-xs text-gray-400 mb-3">
                    Linked on {formatDate(platformData.platforms.google.linkedAt)}
                    {!platformData.platforms.google.hasValidToken && (
                      <span className="text-yellow-400 ml-2">• Token expired</span>
                    )}
                  </div>
                )}

                <div className="flex space-x-2">
                  {platformData.platforms.google.connected ? (
                    <>
                      <button
                        onClick={() => handleLinkPlatform('google')}
                        className="flex-1 bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 border border-blue-500/30 px-3 py-2 rounded-lg text-sm transition-colors flex items-center justify-center space-x-1"
                      >
                        <ExternalLink className="w-4 h-4" />
                        <span>Refresh</span>
                      </button>
                      <button
                        onClick={() => handleUnlinkPlatform('google')}
                        disabled={unlinkingPlatform === 'google'}
                        className="flex-1 bg-red-500/20 hover:bg-red-500/30 text-red-400 border border-red-500/30 px-3 py-2 rounded-lg text-sm transition-colors flex items-center justify-center space-x-1 disabled:opacity-50"
                      >
                        {unlinkingPlatform === 'google' ? (
                          <div className="w-4 h-4 border-2 border-red-400 border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <>
                            <Unlink className="w-4 h-4" />
                            <span>Unlink</span>
                          </>
                        )}
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => handleLinkPlatform('google')}
                      disabled={linkingPlatform === 'google'}
                      className="w-full bg-red-500 hover:bg-red-600 disabled:bg-red-400 disabled:cursor-not-allowed text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center justify-center space-x-1"
                    >
                      {linkingPlatform === 'google' ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>Connecting...</span>
                        </>
                      ) : (
                        <>
                          <ExternalLink className="w-4 h-4" />
                          <span>Connect Google</span>
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>

              {/* Facebook Platform */}
              <div className="bg-gray-700/50 border border-gray-600/50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">f</span>
                    </div>
                    <div>
                      <h3 className="text-white font-medium">Facebook</h3>
                      <p className="text-gray-400 text-sm">
                        {platformData.platforms.facebook.connected ? 'Connected' : 'Not connected'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {platformData.platforms.facebook.connected ? (
                      <Check className="w-5 h-5 text-green-400" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-gray-400" />
                    )}
                  </div>
                </div>
                
                {platformData.platforms.facebook.connected && (
                  <div className="text-xs text-gray-400 mb-3">
                    Linked on {formatDate(platformData.platforms.facebook.linkedAt)}
                    {!platformData.platforms.facebook.hasValidToken && (
                      <span className="text-yellow-400 ml-2">• Token expired</span>
                    )}
                  </div>
                )}

                <div className="flex space-x-2">
                  {platformData.platforms.facebook.connected ? (
                    <>
                      <button
                        onClick={() => handleLinkPlatform('facebook')}
                        className="flex-1 bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 border border-blue-500/30 px-3 py-2 rounded-lg text-sm transition-colors flex items-center justify-center space-x-1"
                      >
                        <ExternalLink className="w-4 h-4" />
                        <span>Refresh</span>
                      </button>
                      <button
                        onClick={() => handleUnlinkPlatform('facebook')}
                        disabled={unlinkingPlatform === 'facebook'}
                        className="flex-1 bg-red-500/20 hover:bg-red-500/30 text-red-400 border border-red-500/30 px-3 py-2 rounded-lg text-sm transition-colors flex items-center justify-center space-x-1 disabled:opacity-50"
                      >
                        {unlinkingPlatform === 'facebook' ? (
                          <div className="w-4 h-4 border-2 border-red-400 border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <>
                            <Unlink className="w-4 h-4" />
                            <span>Unlink</span>
                          </>
                        )}
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => handleLinkPlatform('facebook')}
                      disabled={linkingPlatform === 'facebook'}
                      className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed text-white px-3 py-2 rounded-lg text-sm transition-colors flex items-center justify-center space-x-1"
                    >
                      {linkingPlatform === 'facebook' ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>Connecting...</span>
                        </>
                      ) : (
                        <>
                          <ExternalLink className="w-4 h-4" />
                          <span>Connect Facebook</span>
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>

              {/* Security Note */}
              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <AlertCircle className="w-4 h-4 text-yellow-400 flex-shrink-0 mt-0.5" />
                  <div className="text-xs text-yellow-400">
                    <p className="font-medium mb-1">Security Note</p>
                    <p>
                      {platformData.hasPassword 
                        ? 'You can safely unlink platforms since you have a password set.'
                        : 'Set a password to have a backup authentication method before unlinking platforms.'
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : null}
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-600">
          <button
            onClick={onClose}
            className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};
